import { RATE_LIMIT } from "../../../config/rateLimit.js";
import { createCharacter } from "./charactersController.js";

/** @param {import("fastify").FastifyInstance} fastify */
export const charactersRouter = async (fastify) => {
	await fastify.register(import("@fastify/rate-limit"), RATE_LIMIT.default);

	fastify.route({
		method: "POST",
		url: "/characters",
		schema: createCharacter.schema,
		handler: createCharacter.handler,
	});
};
