// @ts-check

import { RecordId } from "surrealdb";
import { buildEntityId, validateEntityFields } from "../../shared/domain/entityService.js";

/**
 * @typedef {Object} IUserDto
 * @property {string} id
 * @property {string} email
 * @property {string} [createdAt]
 */

/**
 * @typedef {Object} INewUserDto
 * @property {string} [id]
 * @property {string} email
 */

export class User {
	static fields = /** @type {const} */ (["id", "username", "email"]);

	/** @type {RecordId | undefined} */
	id;
	/** @type {string} */
	email;
	/** @type {string | undefined} */
	createdAt;

	/**
	 * @private
	 */
	constructor(payload) {
		validateEntityFields({ entity: User, payload });

		buildEntityId(this, payload.id);
		this.email = payload.email;
		this.createdAt = payload.createdAt;
	}

	/**
	 * @param {IUserDto} payload
	 * @returns {User}
	 */
	static build(payload) {
		return new User(payload);
	}

	/**
	 * @param {INewUserDto} payload
	 * @returns {User}
	 */
	static buildNew(payload) {
		return new User({
			email: payload.email,
		});
	}
}
