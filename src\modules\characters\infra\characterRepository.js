// @ts-check

import { getSurreal } from "../../shared/infra/db/surrealdb/client.js";
import { dbResultHandler } from "../../shared/infra/db/surrealdb/dbResultHandler.js";
// biome-ignore lint/correctness/noUnusedImports: used for jsdoc
import { Character } from "../domain/characterEntity.js";

export const charactersRepository = {
	/**
	 * @param {Character} character
	 */
	insertOne: async (character) => {
		// @ts-ignore
		const response = await dbResultHandler.try(getSurreal().create("characters", character));

		return dbResultHandler.findOne(response);
	},

	/**
	 *
	 * @param {typeof Character["user"]} userId
	 * @returns
	 */
	findMany: async (userId) => {
		const response = await dbResultHandler.try(
			getSurreal().query(
				/* surrealql */ `
					SELECT *
					FROM characters
					WHERE user = $userId
				`,
				{
					userId,
				},
			),
		);

		return dbResultHandler.findMany(response);
	},
};
