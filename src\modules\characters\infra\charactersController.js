// @ts-check

import { buildResponseError } from "../../../utils/common/buildResponseError.js";
import { DB_ERROR_IDS } from "../../shared/infra/db/surrealdb/dbResultHandler.js";
import { Character } from "../domain/characterEntity.js";
import { charactersRepository } from "./characterRepository.js";

export const createCharacter = {
	/** @type {import("../../../server.js").FastifyRouteSchema} */
	schema: {
		body: {
			type: "object",
			required: ["userId"],
			properties: {
				userId: {
					$ref: "http://pop.app/users#/properties/id",
				},
				name: {
					type: "string",
					minLength: 3,
					maxLength: 64,
				},
			},
		},
	},
	/** @type {import("../../../server.js").FastifyController} */
	handler: async (req, res) => {
		// @ts-ignore
		const { userId, name } = req.body;

		const character = Character.buildNew({
			user: userId,
			name,
		});

		const { success, errorId } = await charactersRepository.insertOne(character);

		if (!success) {
			res.status(errorId === DB_ERROR_IDS.alreadyExists ? 409 : 500);

			return buildResponseError({
				entityId: "character",
				errorCode: {
					code: errorId,
					description: "Character name already exists",
				},
			});
		}

		return {
			message: "User created",
		};
	},
};
