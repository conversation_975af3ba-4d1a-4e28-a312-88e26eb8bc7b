// @ts-check

// biome-ignore lint: used for jsdoc
import { RecordId } from "surrealdb";
import { toRecordId } from "../infra/db/surrealdb/toRecordId.js";

/**
 * @param {Object} options
 * @param {Record<string, any>} options.entity
 * @param {Record<string, any>} options.payload
 * @param {boolean} [options.checkId=false]
 */
export function validateEntityFields({ entity, payload, checkId = false }) {
	for (const field of entity.fields) {
		if (!payload[field] && checkId && field !== "id") {
			throw new Error(`${entity.constructor.name} must have a ${field}`);
		}
	}
}

/**
 * @param {string} [id]
 *
 * @return {RecordId | undefined}
 */
export function buildEntityId(id) {
	if (!id) return undefined;

	const recordId = toRecordId(id);

	return recordId || undefined;
}
